### Code Design
- Don't add a big docblock for each function or attribute you add. Just the description about it would be fine unless I ask you to add details to the docblock
- Follow the action pattern when creating new routes and controllers. Action patter is creating an action class, and the logic will be in it. This action class will be injected in the route action in the controller.

### Authentication
- User prefers email-based authentication to mobile-based authentication for login systems.
- For password reset APIs: use actions pattern, implement rate limiting for security, store reset tokens with expiration in the password_resets table, use <PERSON><PERSON>'s built-in password reset functionality when available.

### Writing tests
- Write test cases using a Pest PHP testing framework, not PHPUnit.
- Each controller in the Http folder should have a test for it with the same path of the controller in the tests' folder.
- Wrap every function in the controller with the describe() function from PestPHP framework and inside it cover all the cases for that function.
- Make sure all the tests you write are passing without any issues.
- When writing test for each route, make sure you cover 100% of the code.
- When you name the tests and describe functions, write the role of the user then the name of the test. For example (Sales: create new quote).
- If the route is not for a specific role, and it is accessible from all types of users, then write at the beginning of the name Users: for example (Users: login)
- For AuthController tests: create test files matching controller path structure, use describe() blocks for each method, ensure 100% code coverage including all scenarios (success, validation failures, auth errors, edge cases), and test integration with action classes if using an action pattern.
- Make use of the helper functions inside the Pest.php file. for example, to log in a user use the userLogin($user) function.
- For password reset APIs tests: test with Pest PHP using the 'Users:' prefix for general access endpoints.