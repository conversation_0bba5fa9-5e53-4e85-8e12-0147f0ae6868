<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\SoftDeletes;

class MarketingPlan extends Model
{
    use HasFactory, SoftDeletes;

    protected $fillable = [
        'app_id',
        'name',
        'description',
        'formality_level',
        'friendliness_level',
        'tone',
        'target_audience',
        'posting_frequency',
        'content_types',
        'ai_instructions',
        'is_active',
    ];

    protected $casts = [
        'formality_level' => 'integer',
        'friendliness_level' => 'integer',
        'content_types' => 'array',
        'is_active' => 'boolean',
    ];

    public function app(): BelongsTo
    {
        return $this->belongsTo(App::class);
    }

    public function posts(): HasMany
    {
        return $this->hasMany(Post::class);
    }
}
