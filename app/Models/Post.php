<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\SoftDeletes;

class Post extends Model
{
    use HasFactory, SoftDeletes;

    protected $fillable = [
        'marketing_plan_id',
        'title',
        'content',
        'image_url',
        'ai_prompt_used',
        'status',
        'scheduled_at',
        'published_at',
    ];

    protected $casts = [
        'scheduled_at' => 'datetime',
        'published_at' => 'datetime',
    ];

    public function marketingPlan(): BelongsTo
    {
        return $this->belongsTo(MarketingPlan::class);
    }

    public function postSocialAccounts(): HasMany
    {
        return $this->hasMany(PostSocialAccount::class);
    }
}
