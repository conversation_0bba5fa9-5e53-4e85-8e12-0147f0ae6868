<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class PostStatistic extends Model
{
    use HasFactory;

    protected $fillable = [
        'post_social_account_id',
        'views',
        'likes',
        'shares',
        'comments',
        'clicks',
        'reach',
        'impressions',
        'engagement_rate',
        'collected_at',
    ];

    protected $casts = [
        'views' => 'integer',
        'likes' => 'integer',
        'shares' => 'integer',
        'comments' => 'integer',
        'clicks' => 'integer',
        'reach' => 'integer',
        'impressions' => 'integer',
        'engagement_rate' => 'decimal:2',
        'collected_at' => 'datetime',
    ];

    public function postSocialAccount(): BelongsTo
    {
        return $this->belongsTo(PostSocialAccount::class);
    }
}
