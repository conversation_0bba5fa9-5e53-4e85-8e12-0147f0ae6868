<?php

use Carbon\Carbon;
use Filament\Forms\Components\DatePicker;
use Filament\Forms\Components\DateTimePicker;
use Filament\Forms\Components\FileUpload;
use Filament\Forms\Components\Select;
use Filament\Forms\Components\Textarea;
use Filament\Forms\Components\TextInput;
use Filament\Forms\Components\Toggle;
use Filament\Infolists\Components\TextEntry;
use Filament\Tables\Columns\ImageColumn;
use Filament\Tables\Columns\TextColumn;
use Filament\Tables\Columns\ToggleColumn;
use Filament\Tables\Filters\Filter;
use Filament\Tables\Filters\Indicator;
use Filament\Tables\Filters\QueryBuilder\Constraints\BooleanConstraint;
use Filament\Tables\Filters\QueryBuilder\Constraints\DateConstraint;
use Filament\Tables\Filters\QueryBuilder\Constraints\TextConstraint;
use Filament\Tables\Table;
use Illuminate\Contracts\Routing\ResponseFactory;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Foundation\Application;
use Illuminate\Http\Response;

if (! function_exists('successResponse')) {
    function successResponse($message = null, $data = null): Application|Response|ResponseFactory
    {
        $response = [
            'message' => $message ?: __('Done Successfully'),
        ];

        if ($data) {
            $response['data'] = $data;
        }

        return response($response, Response::HTTP_OK);
    }
}

if (! function_exists('errorResponse')) {
    function errorResponse($message = null, $status = Response::HTTP_UNPROCESSABLE_ENTITY, $data = null): Application|Response|ResponseFactory
    {
        $response = [
            'message' => $message ?: __('Something went wrong'),
        ];

        if ($data) {
            $response['data'] = $data;
        }

        return response($response, $status);
    }
}

if (! function_exists('storage')) {
    function storage($path, $multiple = false): string|array
    {
        if ($multiple && is_array($path)) {
            $paths = [];
            foreach ($path as $p) {
                $paths[] = storage($p);
            }

            return $paths;
        }
        if (is_array($path) && count($path)) {
            $path = Arr::first($path);
        }
        if (is_string($path)) {
            if (filter_var(str($path)->replace(' ', '_'), FILTER_VALIDATE_URL)) {
                return $path;
            }

            if (Storage::exists($path)) {
                return Storage::url($path);
            }
        }

        return '';
    }
}

if (! function_exists('enumValue')) {
    function enumValue($value): ?string
    {
        if (is_string($value)) {
            return $value;
        }

        return $value?->value;
    }
}

if (! function_exists('locale')) {
    function locale($locale = null): string
    {
        return str($locale ?: app()->getLocale())->contains('en') ? 'en' : 'ar';
    }
}

if (! function_exists('createdAtFilter')) {
    function createdAtFilter($name = 'created_at')
    {
        return Filter::make($name)
            ->form([
                DatePicker::make('from'),
                DatePicker::make('to'),
            ])
            ->query(function (Builder $query, $data) use ($name) {
                return $query->when($data['from'], function (Builder $query) use ($data, $name) {
                    $query->where($name, '>=', $data['from']);
                })->when($data['to'], function (Builder $query) use ($data, $name) {
                    $query->where($name, '<=', $data['to']);
                });
            })
            ->indicateUsing(function (array $data) use ($name): array {
                $indicators = [];

                if ($data['from'] ?? null) {
                    $indicators[] = Indicator::make(str($name)->replace('_', ' ')->title()->toString() . ' from ' . Carbon::parse($data['from'])->toFormattedDateString())
                        ->removeField('from');
                }

                if ($data['to'] ?? null) {
                    $indicators[] = Indicator::make(str($name)->replace('_', ' ')->title()->toString() . ' until ' . Carbon::parse($data['to'])->toFormattedDateString())
                        ->removeField('to');
                }

                return $indicators;
            });
    }
}

if (! function_exists('configureFilamentComponents')) {
    function configureFilamentComponents(): void
    {
        TextInput::configureUsing(function (TextInput $input) {
            $input->maxLength(255)
                ->label(__($input->getLabel()));
        });

        Select::configureUsing(function (Select $select) {
            $select->native(false)
                ->searchable()
                ->preload()
                ->label(__($select->getLabel()));
        });

        DatePicker::configureUsing(function (DatePicker $datePicker) {
            $datePicker->native(false)
                ->label(__($datePicker->getLabel()));
        });

        DateTimePicker::configureUsing(function (DateTimePicker $datePicker) {
            $datePicker->native(false)
                ->label(__($datePicker->getLabel()));
        });

        Toggle::configureUsing(function (Toggle $toggle) {
            $toggle->label(__($toggle->getLabel()));
        });

        FileUpload::configureUsing(function (FileUpload $fileUpload) {
            $fileUpload->maxSize(5 * 1024)
                ->imageEditor()
                ->label(__($fileUpload->getLabel()));
        });

        Textarea::configureUsing(function (Textarea $textarea) {
            $textarea->rows(6)
                ->label(__($textarea->getLabel()));
        });

        TextColumn::configureUsing(function (TextColumn $column) {
            $column->label(__($column->getLabel()));
        });

        ImageColumn::configureUsing(function (ImageColumn $column) {
            $column->label(__($column->getLabel()));
        });

        ToggleColumn::configureUsing(function (ToggleColumn $column) {
            $column->label(__($column->getLabel()));
        });

        TextEntry::configureUsing(function (TextEntry $column) {
            $column->label(__($column->getLabel()));
        });

        TextConstraint::configureUsing(function (TextConstraint $column) {
            $column->label(__($column->getLabel()));
        });

        BooleanConstraint::configureUsing(function (BooleanConstraint $column) {
            $column->label(__($column->getLabel()));
        });

        DateConstraint::configureUsing(function (DateConstraint $column) {
            $column->label(__($column->getLabel()));
        });

        Table::configureUsing(function (Table $table) {
            $table->defaultSort('created_at', 'desc');
        });
    }
}

if (! function_exists('intWithStyle')) {
    function intWithStyle($n)
    {
        if ($n < 1000) {
            return $n;
        }
        $suffix = ['', 'k', 'M', 'G', 'T', 'P', 'E', 'Z', 'Y'];
        $power = floor(log($n, 1000));

        return round($n / (1000 ** $power), 1, PHP_ROUND_HALF_EVEN) . $suffix[$power];
    }
}
