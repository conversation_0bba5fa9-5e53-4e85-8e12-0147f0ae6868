<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        Schema::create('marketing_plans', function (Blueprint $table) {
            $table->id();
            $table->foreignId('app_id')->constrained()->onDelete('cascade');
            $table->string('name');
            $table->text('description')->nullable();
            $table->integer('formality_level')->default(5)->comment('1-10 scale');
            $table->integer('friendliness_level')->default(5)->comment('1-10 scale');
            $table->enum('tone', ['professional', 'casual', 'humorous', 'authoritative', 'friendly'])->default('professional');
            $table->text('target_audience')->nullable();
            $table->enum('posting_frequency', ['daily', 'weekly', 'bi-weekly', 'monthly'])->default('weekly');
            $table->json('content_types')->nullable()->comment('Array of content types: text, image, video, link');
            $table->text('ai_instructions')->nullable();
            $table->boolean('is_active')->default(true);
            $table->timestamps();
            $table->softDeletes();

            $table->index(['app_id', 'is_active']);
        });
    }

    public function down(): void
    {
        Schema::dropIfExists('marketing_plans');
    }
};
