<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        Schema::create('post_statistics', function (Blueprint $table) {
            $table->id();
            $table->foreignId('post_social_account_id')->constrained()->onDelete('cascade');
            $table->bigInteger('views')->default(0);
            $table->bigInteger('likes')->default(0);
            $table->bigInteger('shares')->default(0);
            $table->bigInteger('comments')->default(0);
            $table->bigInteger('clicks')->default(0);
            $table->bigInteger('reach')->default(0);
            $table->bigInteger('impressions')->default(0);
            $table->decimal('engagement_rate', 5, 2)->default(0.00);
            $table->timestamp('collected_at');
            $table->timestamps();

            $table->index(['post_social_account_id', 'collected_at']);
        });
    }

    public function down(): void
    {
        Schema::dropIfExists('post_statistics');
    }
};
