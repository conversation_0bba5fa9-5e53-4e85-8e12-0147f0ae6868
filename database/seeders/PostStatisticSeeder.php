<?php

namespace Database\Seeders;

use App\Models\PostSocialAccount;
use App\Models\PostStatistic;
use Illuminate\Database\Seeder;

class PostStatisticSeeder extends Seeder
{
    public function run(): void
    {
        // Create statistics for published posts only
        PostSocialAccount::where('status', 'published')->each(function ($postSocialAccount) {
            // Create 1-5 statistics entries (showing data collection over time)
            PostStatistic::factory(rand(1, 5))->create([
                'post_social_account_id' => $postSocialAccount->id,
            ]);
        });

        // Create some high-performing post statistics
        $publishedPosts = PostSocialAccount::where('status', 'published')->limit(5)->get();
        foreach ($publishedPosts as $postSocialAccount) {
            PostStatistic::factory()->highEngagement()->create([
                'post_social_account_id' => $postSocialAccount->id,
            ]);
        }

        // Create some low-performing post statistics
        $publishedPosts = PostSocialAccount::where('status', 'published')->skip(5)->limit(3)->get();
        foreach ($publishedPosts as $postSocialAccount) {
            PostStatistic::factory()->lowEngagement()->create([
                'post_social_account_id' => $postSocialAccount->id,
            ]);
        }
    }
}
