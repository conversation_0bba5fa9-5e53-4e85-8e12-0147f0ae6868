<?php

namespace Database\Seeders;

use App\Models\User;
use App\Models\UserPreference;
use Illuminate\Database\Seeder;

class UserPreferenceSeeder extends Seeder
{
    public function run(): void
    {
        // Create preferences for existing users
        User::whereDoesntHave('userPreference')->each(function ($user) {
            UserPreference::factory()->create([
                'user_id' => $user->id,
            ]);
        });

        // Create additional standalone preferences
        UserPreference::factory(5)->create();
    }
}
