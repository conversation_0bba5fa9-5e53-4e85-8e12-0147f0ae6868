<?php

namespace Database\Seeders;

use App\Models\App;
use App\Models\SocialAccount;
use App\Models\User;
use Illuminate\Database\Seeder;

class SocialAccountSeeder extends Seeder
{
    public function run(): void
    {
        // Create personal social accounts for users
        User::all()->each(function ($user) {
            // Each user gets 1-2 personal social accounts
            SocialAccount::factory(rand(1, 2))->create([
                'user_id' => $user->id,
                'app_id' => null, // Personal accounts
            ]);
        });

        // Create app-specific social accounts
        App::all()->each(function ($app) {
            // Each app gets 0-2 social accounts
            if (rand(0, 100) > 30) { // 70% chance of having social accounts
                SocialAccount::factory(rand(1, 2))->create([
                    'user_id' => $app->user_id,
                    'app_id' => $app->id,
                ]);
            }
        });

        // Create some specific demo accounts
        $demoUser = User::first();
        $demoApp = App::first();

        if ($demoUser && $demoApp) {
            SocialAccount::factory()->twitter()->create([
                'user_id' => $demoUser->id,
                'app_id' => null,
                'username' => 'demo_user_personal',
                'display_name' => 'Demo User',
            ]);

            SocialAccount::factory()->linkedin()->create([
                'user_id' => $demoUser->id,
                'app_id' => $demoApp->id,
                'username' => 'techstartup-inc',
                'display_name' => 'TechStartup Inc.',
            ]);
        }
    }
}
