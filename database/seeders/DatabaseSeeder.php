<?php

namespace Database\Seeders;

use App\Models\User;
use Illuminate\Database\Seeder;

class DatabaseSeeder extends Seeder
{
    public function run(): void
    {
        // Create test users
        User::factory()->create([
            'name' => 'Test User',
            'email' => '<EMAIL>',
            'password' => 'password',
            'subscription_plan' => 'premium',
        ]);

        User::factory()->create([
            'name' => 'Demo User',
            'email' => '<EMAIL>',
            'password' => 'password',
            'subscription_plan' => 'free',
        ]);

        // Create additional users
        User::factory(8)->create();

        // Run all seeders in proper order
        $this->call([
            UserPreferenceSeeder::class,
            AppSeeder::class,
            SocialAccountSeeder::class,
            MarketingPlanSeeder::class,
            PostSeeder::class,
            PostStatisticSeeder::class,
        ]);

        $this->command->info('✅ Marketing automation database seeded successfully!');
        $this->command->info('📊 Created comprehensive test data for development environment');
    }
}
