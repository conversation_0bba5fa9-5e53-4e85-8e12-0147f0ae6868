<?php

namespace Database\Seeders;

use App\Models\App;
use App\Models\User;
use Illuminate\Database\Seeder;

class AppSeeder extends Seeder
{
    public function run(): void
    {
        // Create apps for existing users
        User::all()->each(function ($user) {
            App::factory(rand(1, 3))->create([
                'user_id' => $user->id,
            ]);
        });

        // Create some additional apps with new users
        App::factory(10)->create();

        // Create some specific demo apps
        $demoUser = User::first();
        if ($demoUser) {
            App::factory()->create([
                'user_id' => $demoUser->id,
                'name' => 'TechStartup Inc.',
                'url' => 'https://techstartup.com',
                'description' => 'Innovative SaaS solutions for modern businesses',
                'industry' => 'Technology',
                'status' => 'active',
            ]);

            App::factory()->create([
                'user_id' => $demoUser->id,
                'name' => 'Healthy Living Blog',
                'url' => 'https://healthyliving.blog',
                'description' => 'Your guide to wellness and healthy lifestyle choices',
                'industry' => 'Health & Wellness',
                'status' => 'active',
            ]);
        }
    }
}
