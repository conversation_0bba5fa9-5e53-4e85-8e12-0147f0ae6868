<?php

namespace Database\Seeders;

use App\Models\App;
use App\Models\MarketingPlan;
use Illuminate\Database\Seeder;

class MarketingPlanSeeder extends Seeder
{
    public function run(): void
    {
        // Create marketing plans for existing apps
        App::all()->each(function ($app) {
            // Each app gets 1-3 marketing plans
            MarketingPlan::factory(rand(1, 3))->create([
                'app_id' => $app->id,
            ]);
        });

        // Create some specific demo marketing plans
        $demoApp = App::first();
        if ($demoApp) {
            MarketingPlan::factory()->professional()->create([
                'app_id' => $demoApp->id,
                'name' => 'B2B Lead Generation Campaign',
                'description' => 'Professional campaign targeting enterprise clients with formal tone and industry insights.',
                'target_audience' => 'Enterprise decision makers and IT professionals',
                'posting_frequency' => 'weekly',
                'content_types' => ['text', 'image', 'link'],
                'ai_instructions' => 'Focus on industry expertise, case studies, and professional insights. Use data-driven content.',
            ]);

            MarketingPlan::factory()->casual()->create([
                'app_id' => $demoApp->id,
                'name' => 'Community Engagement Strategy',
                'description' => 'Casual, friendly approach to build community and increase brand awareness.',
                'target_audience' => 'Small business owners and entrepreneurs',
                'posting_frequency' => 'daily',
                'content_types' => ['text', 'image', 'video'],
                'ai_instructions' => 'Use conversational tone, share behind-the-scenes content, and encourage community interaction.',
            ]);
        }
    }
}
