<?php

namespace Database\Seeders;

use App\Models\MarketingPlan;
use App\Models\Post;
use App\Models\PostSocialAccount;
use App\Models\SocialAccount;
use Illuminate\Database\Seeder;

class PostSeeder extends Seeder
{
    public function run(): void
    {
        // Create posts for existing marketing plans
        MarketingPlan::all()->each(function ($plan) {
            // Each plan gets 5-15 posts
            $posts = Post::factory(rand(5, 15))->create([
                'marketing_plan_id' => $plan->id,
            ]);

            // For each post, create social account associations
            $posts->each(function ($post) use ($plan) {
                // Get social accounts for this app or user
                $socialAccounts = SocialAccount::where('user_id', $plan->app->user_id)
                    ->where(function ($query) use ($plan) {
                        $query->where('app_id', $plan->app_id)
                            ->orWhereNull('app_id');
                    })
                    ->where('is_active', true)
                    ->get();

                // Associate post with 1-2 social accounts
                $selectedAccounts = $socialAccounts->random(min($socialAccounts->count(), rand(1, 2)));

                foreach ($selectedAccounts as $account) {
                    PostSocialAccount::factory()->create([
                        'post_id' => $post->id,
                        'social_account_id' => $account->id,
                    ]);
                }
            });
        });

        // Create some additional standalone posts
        Post::factory(20)->create();
    }
}
