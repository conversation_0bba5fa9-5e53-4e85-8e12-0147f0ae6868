<?php

namespace Database\Factories;

use App\Models\User;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\UserPreference>
 */
class UserPreferenceFactory extends Factory
{
    public function definition(): array
    {
        return [
            'user_id' => User::factory(),
            'notification_settings' => [
                'email_notifications' => $this->faker->boolean(80),
                'push_notifications' => $this->faker->boolean(60),
                'marketing_emails' => $this->faker->boolean(40),
                'weekly_reports' => $this->faker->boolean(70),
            ],
            'ai_preferences' => [
                'default_tone' => $this->faker->randomElement(['professional', 'casual', 'friendly']),
                'default_formality' => $this->faker->numberBetween(1, 10),
                'default_friendliness' => $this->faker->numberBetween(1, 10),
                'preferred_content_length' => $this->faker->randomElement(['short', 'medium', 'long']),
                'include_emojis' => $this->faker->boolean(60),
                'include_hashtags' => $this->faker->boolean(80),
            ],
        ];
    }
}
