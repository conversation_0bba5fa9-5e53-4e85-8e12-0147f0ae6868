<?php

namespace Database\Factories;

use App\Models\MarketingPlan;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\Post>
 */
class PostFactory extends Factory
{
    public function definition(): array
    {
        $status = $this->faker->randomElement(['draft', 'scheduled', 'published', 'failed']);
        $hasImage = $this->faker->boolean(40);

        $marketingContent = [
            "🚀 Exciting news! We're launching something amazing this week. Stay tuned for updates!",
            "💡 Pro tip: Small changes can lead to big results. What's one thing you're improving today?",
            '🌟 Customer success story: How we helped increase productivity by 300%',
            '📈 Industry insights: The latest trends that are shaping our market',
            "🎯 Behind the scenes: A look at our team's creative process",
            '💪 Monday motivation: Turn your challenges into opportunities',
            '🔥 Limited time offer: Get 30% off our premium features this week',
            '📚 Educational content: 5 tips to optimize your workflow',
            "🤝 Partnership announcement: We're excited to collaborate with industry leaders",
            '🎉 Milestone celebration: Thank you for helping us reach 10K followers!',
        ];

        return [
            'marketing_plan_id' => MarketingPlan::factory(),
            'title' => $this->faker->optional(0.6)->sentence(6),
            'content' => $this->faker->randomElement($marketingContent),
            'image_url' => $hasImage ? $this->faker->imageUrl(800, 600, 'business', true) : null,
            'ai_prompt_used' => $this->faker->optional(0.8)->paragraph(),
            'status' => $status,
            'scheduled_at' => $status === 'scheduled' ? $this->faker->dateTimeBetween('now', '+1 month') : null,
            'published_at' => $status === 'published' ? $this->faker->dateTimeBetween('-1 month', 'now') : null,
        ];
    }

    public function draft(): static
    {
        return $this->state(fn (array $attributes) => [
            'status' => 'draft',
            'scheduled_at' => null,
            'published_at' => null,
        ]);
    }

    public function scheduled(): static
    {
        return $this->state(fn (array $attributes) => [
            'status' => 'scheduled',
            'scheduled_at' => $this->faker->dateTimeBetween('now', '+1 month'),
            'published_at' => null,
        ]);
    }

    public function published(): static
    {
        return $this->state(fn (array $attributes) => [
            'status' => 'published',
            'scheduled_at' => $this->faker->dateTimeBetween('-1 month', 'now'),
            'published_at' => $this->faker->dateTimeBetween('-1 month', 'now'),
        ]);
    }
}
