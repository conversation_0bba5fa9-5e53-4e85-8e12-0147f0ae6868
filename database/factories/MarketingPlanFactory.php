<?php

namespace Database\Factories;

use App\Models\App;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\MarketingPlan>
 */
class MarketingPlanFactory extends Factory
{
    public function definition(): array
    {
        $planNames = [
            'Brand Awareness Campaign', 'Product Launch Strategy', 'Customer Engagement Plan',
            'Social Media Growth', 'Lead Generation Campaign', 'Content Marketing Strategy',
            'Holiday Marketing Plan', 'Influencer Outreach', 'Community Building Initiative',
        ];

        $audiences = [
            'Young professionals aged 25-35', 'Small business owners', 'Tech enthusiasts',
            'Health and wellness seekers', 'Budget-conscious families', 'Luxury consumers',
            'Students and recent graduates', 'Remote workers', 'Eco-conscious consumers',
        ];

        return [
            'app_id' => App::factory(),
            'name' => $this->faker->randomElement($planNames),
            'description' => $this->faker->paragraph(2),
            'formality_level' => $this->faker->numberBetween(1, 10),
            'friendliness_level' => $this->faker->numberBetween(1, 10),
            'tone' => $this->faker->randomElement(['professional', 'casual', 'humorous', 'authoritative', 'friendly']),
            'target_audience' => $this->faker->randomElement($audiences),
            'posting_frequency' => $this->faker->randomElement(['daily', 'weekly', 'bi-weekly', 'monthly']),
            'content_types' => $this->faker->randomElements(['text', 'image', 'video', 'link'], $this->faker->numberBetween(1, 4)),
            'ai_instructions' => $this->faker->optional(0.7)->paragraph(),
            'is_active' => $this->faker->boolean(80),
        ];
    }

    public function active(): static
    {
        return $this->state(fn (array $attributes) => [
            'is_active' => true,
        ]);
    }

    public function professional(): static
    {
        return $this->state(fn (array $attributes) => [
            'tone' => 'professional',
            'formality_level' => $this->faker->numberBetween(7, 10),
            'friendliness_level' => $this->faker->numberBetween(3, 6),
        ]);
    }

    public function casual(): static
    {
        return $this->state(fn (array $attributes) => [
            'tone' => 'casual',
            'formality_level' => $this->faker->numberBetween(1, 4),
            'friendliness_level' => $this->faker->numberBetween(7, 10),
        ]);
    }
}
