<?php

namespace Database\Factories;

use App\Models\PostSocialAccount;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\PostStatistic>
 */
class PostStatisticFactory extends Factory
{
    public function definition(): array
    {
        $impressions = $this->faker->numberBetween(100, 50000);
        $reach = $this->faker->numberBetween(50, $impressions);
        $views = $this->faker->numberBetween(10, $reach);
        $likes = $this->faker->numberBetween(0, intval($views * 0.1));
        $shares = $this->faker->numberBetween(0, intval($likes * 0.3));
        $comments = $this->faker->numberBetween(0, intval($likes * 0.2));
        $clicks = $this->faker->numberBetween(0, intval($views * 0.05));

        // Calculate engagement rate: (likes + shares + comments) / impressions * 100
        $engagements = $likes + $shares + $comments;
        $engagementRate = $impressions > 0 ? round(($engagements / $impressions) * 100, 2) : 0;

        return [
            'post_social_account_id' => PostSocialAccount::factory(),
            'views' => $views,
            'likes' => $likes,
            'shares' => $shares,
            'comments' => $comments,
            'clicks' => $clicks,
            'reach' => $reach,
            'impressions' => $impressions,
            'engagement_rate' => $engagementRate,
            'collected_at' => $this->faker->dateTimeBetween('-1 month', 'now'),
        ];
    }

    public function highEngagement(): static
    {
        return $this->state(function (array $attributes) {
            $impressions = $this->faker->numberBetween(10000, 100000);
            $reach = $this->faker->numberBetween(5000, $impressions);
            $views = $this->faker->numberBetween(1000, $reach);
            $likes = $this->faker->numberBetween(intval($views * 0.05), intval($views * 0.15));
            $shares = $this->faker->numberBetween(intval($likes * 0.1), intval($likes * 0.4));
            $comments = $this->faker->numberBetween(intval($likes * 0.1), intval($likes * 0.3));
            $clicks = $this->faker->numberBetween(intval($views * 0.02), intval($views * 0.08));

            $engagements = $likes + $shares + $comments;
            $engagementRate = round(($engagements / $impressions) * 100, 2);

            return [
                'views' => $views,
                'likes' => $likes,
                'shares' => $shares,
                'comments' => $comments,
                'clicks' => $clicks,
                'reach' => $reach,
                'impressions' => $impressions,
                'engagement_rate' => $engagementRate,
            ];
        });
    }

    public function lowEngagement(): static
    {
        return $this->state(function (array $attributes) {
            $impressions = $this->faker->numberBetween(50, 1000);
            $reach = $this->faker->numberBetween(25, $impressions);
            $views = $this->faker->numberBetween(5, $reach);
            $likes = $this->faker->numberBetween(0, intval($views * 0.02));
            $shares = $this->faker->numberBetween(0, intval($likes * 0.1));
            $comments = $this->faker->numberBetween(0, intval($likes * 0.1));
            $clicks = $this->faker->numberBetween(0, intval($views * 0.01));

            $engagements = $likes + $shares + $comments;
            $engagementRate = $impressions > 0 ? round(($engagements / $impressions) * 100, 2) : 0;

            return [
                'views' => $views,
                'likes' => $likes,
                'shares' => $shares,
                'comments' => $comments,
                'clicks' => $clicks,
                'reach' => $reach,
                'impressions' => $impressions,
                'engagement_rate' => $engagementRate,
            ];
        });
    }
}
