<?php

namespace Database\Factories;

use App\Models\Post;
use App\Models\SocialAccount;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\PostSocialAccount>
 */
class PostSocialAccountFactory extends Factory
{
    public function definition(): array
    {
        $status = $this->faker->randomElement(['pending', 'published', 'failed']);

        return [
            'post_id' => Post::factory(),
            'social_account_id' => SocialAccount::factory(),
            'platform_post_id' => $status === 'published' ? $this->faker->numerify('##########') : null,
            'published_at' => $status === 'published' ? $this->faker->dateTimeBetween('-1 month', 'now') : null,
            'status' => $status,
            'error_message' => $status === 'failed' ? $this->faker->sentence() : null,
        ];
    }

    public function published(): static
    {
        return $this->state(fn (array $attributes) => [
            'status' => 'published',
            'platform_post_id' => $this->faker->numerify('##########'),
            'published_at' => $this->faker->dateTimeBetween('-1 month', 'now'),
            'error_message' => null,
        ]);
    }

    public function failed(): static
    {
        return $this->state(fn (array $attributes) => [
            'status' => 'failed',
            'platform_post_id' => null,
            'published_at' => null,
            'error_message' => $this->faker->randomElement([
                'Authentication failed',
                'Rate limit exceeded',
                'Content violates platform guidelines',
                'Network timeout',
                'Invalid access token',
            ]),
        ]);
    }
}
