<?php

namespace Database\Factories;

use App\Models\User;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\App>
 */
class AppFactory extends Factory
{
    public function definition(): array
    {
        $companyName = $this->faker->company();
        $domain = strtolower(str_replace([' ', '.', ','], '', $companyName)) . '.com';

        return [
            'user_id' => User::factory(),
            'name' => $companyName,
            'url' => 'https://' . $domain,
            'description' => $this->faker->catchPhrase() . '. ' . $this->faker->bs(),
            'logo_url' => $this->faker->imageUrl(200, 200, 'business', true, $companyName),
            'industry' => $this->faker->randomElement([
                'Technology', 'Healthcare', 'Finance', 'Education', 'E-commerce',
                'Real Estate', 'Food & Beverage', 'Travel', 'Fashion', 'Fitness',
                'Consulting', 'Marketing', 'Manufacturing', 'Entertainment',
            ]),
            'status' => $this->faker->randomElement(['active', 'inactive', 'suspended']),
        ];
    }

    public function active(): static
    {
        return $this->state(fn (array $attributes) => [
            'status' => 'active',
        ]);
    }
}
