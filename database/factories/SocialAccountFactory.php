<?php

namespace Database\Factories;

use App\Models\App;
use App\Models\User;
use Illuminate\Database\Eloquent\Factories\Factory;
use Illuminate\Support\Str;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\SocialAccount>
 */
class SocialAccountFactory extends Factory
{
    public function definition(): array
    {
        $platform = $this->faker->randomElement(['twitter', 'linkedin']);
        $username = $this->faker->userName();

        return [
            'user_id' => User::factory(),
            'app_id' => $this->faker->boolean(60) ? App::factory() : null,
            'platform' => $platform,
            'platform_user_id' => $platform === 'twitter' ? $this->faker->numerify('##########') : $this->faker->numerify('########'),
            'username' => $username,
            'display_name' => $this->faker->name(),
            'access_token' => 'fake_access_token_' . Str::random(40),
            'refresh_token' => $this->faker->boolean(70) ? 'fake_refresh_token_' . Str::random(40) : null,
            'expires_at' => $this->faker->boolean(80) ? $this->faker->dateTimeBetween('+1 month', '+1 year') : null,
            'is_active' => $this->faker->boolean(90),
        ];
    }

    public function twitter(): static
    {
        return $this->state(fn (array $attributes) => [
            'platform' => 'twitter',
            'platform_user_id' => $this->faker->numerify('##########'),
        ]);
    }

    public function linkedin(): static
    {
        return $this->state(fn (array $attributes) => [
            'platform' => 'linkedin',
            'platform_user_id' => $this->faker->numerify('########'),
        ]);
    }

    public function personal(): static
    {
        return $this->state(fn (array $attributes) => [
            'app_id' => null,
        ]);
    }
}
