<?php

namespace Database\Factories;

use Illuminate\Database\Eloquent\Factories\Factory;
use Illuminate\Support\Str;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\User>
 */
class UserFactory extends Factory
{
    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        return [
            'name' => fake()->name(),
            'email' => fake()->unique()->safeEmail(),
            'email_verified_at' => now(),
            'password' => fake()->optional(0.3)->password(),
            'workos_id' => 'fake-' . Str::random(10),
            'remember_token' => Str::random(10),
            'avatar' => fake()->imageUrl(200, 200, 'people', true),
            'timezone' => fake()->timezone(),
            'subscription_plan' => fake()->randomElement(['free', 'basic', 'premium', 'enterprise']),
        ];
    }

    /**
     * Indicate that the model's email address should be unverified.
     */
    public function unverified(): static
    {
        return $this->state(fn (array $attributes) => [
            'email_verified_at' => null,
        ]);
    }
}
