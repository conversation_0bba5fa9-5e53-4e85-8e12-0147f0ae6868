<?php

use Illuminate\Support\Facades\Route;
use <PERSON><PERSON>\WorkOS\Http\Requests\AuthKitAuthenticationRequest;
use <PERSON><PERSON>\WorkOS\Http\Requests\AuthKitLoginRequest;
use <PERSON>vel\WorkOS\Http\Requests\AuthKitLogoutRequest;

Route::get('login', function (AuthKitLoginRequest $request) {
    return $request->redirect();
})->middleware(['guest'])->name('login');

Route::get('authenticate', function (AuthKitAuthenticationRequest $request) {
    return tap(to_route('dashboard'), fn () => $request->authenticate());
})->middleware(['guest']);

Route::post('logout', function (AuthKitLogoutRequest $request) {
    return $request->logout();
})->middleware(['auth'])->name('logout');
