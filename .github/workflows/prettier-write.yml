name: Format
on:
    pull_request:
        branches: [main]
jobs:
    format:
        runs-on: ubuntu-latest
        steps:
            - uses: actions/checkout@v2
              with:
                  ref: ${{ github.head_ref }}
            - uses: actions/setup-node@v2
              with:
                  node-version: '14.x'
            - run: npm ci
            - run: npm run format
            - name: Commit changes
              uses: stefanzweifel/git-auto-commit-action@v4
              with:
                  commit_message: Apply formatting changes
                  branch: ${{ github.head_ref }}
