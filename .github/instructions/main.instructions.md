---
applyTo: '**'
---

# AI Coding Agent Instructions for This Project

Welcome! This project is a Laravel application using Inertia.js, React, the Laravel Starter Kit, and ShadeCN UI. Please follow these guidelines to ensure high-quality, maintainable, and consistent code contributions.

## General Principles
- **Follow <PERSON><PERSON> and <PERSON><PERSON> best practices** at all times.
- **Prioritize code readability, maintainability, and scalability.**
- **Write clean, DRY, and SOLID code.**
- **Use type safety** (TypeScript for React, PHP types for <PERSON><PERSON> where possible).
- **Prefer convention over configuration**; follow framework conventions.
- **Document complex logic** with clear comments and docblocks.
- **Write meaningful commit messages** and keep PRs focused.

## Backend (Laravel)
- Use Eloquent models and relationships efficiently.
- Use Form Requests for validation.
- Use Service classes for business logic when needed.
- Use Resource classes for API responses.
- Use policies and gates for authorization.
- Use Laravel's dependency injection and service container.
- Use migrations, factories, and seeders for database changes.
- Write Pest or PHPUnit tests for all new features and bug fixes.
- Use Laravel's localization features for user-facing text.
- Use Laravel's built-in features (queues, events, notifications, etc.) when appropriate.

## Frontend (React + Inertia)
- Use functional components and hooks.
- Use TypeScript for all React code.
- Use ShadeCN UI components for UI consistency.
- Use Inertia.js for client-server communication; avoid direct API calls from React.
- Use React context or state management only when necessary.
- Keep components small and focused; prefer composition over inheritance.
- Use CSS modules or Tailwind CSS for styling (as per project setup).
- Write unit and integration tests for React components.

## Code Quality
- Run and pass all linters and formatters before committing (see `eslint.config.js`, `lint-staged.config.js`).
- Ensure all tests pass before submitting code.
- Avoid introducing breaking changes unless discussed.
- Remove unused code and dependencies.
- Use environment variables for configuration.

## Security
- Sanitize and validate all user input.
- Protect sensitive routes and data.
- Never commit secrets or credentials.

## Collaboration
- Communicate clearly in PRs and issues.
- Ask for feedback when unsure.
- Review code for others when possible.

## References
- [Laravel Documentation](https://laravel.com/docs)
- [Inertia.js Documentation](https://inertiajs.com/)
- [React Documentation](https://react.dev/)
- [ShadeCN UI](https://ui.shadcn.com/)

---

**Thank you for contributing!**
